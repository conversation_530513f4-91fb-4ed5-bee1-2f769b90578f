"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M7 5h14v2H7z"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "4",
  cy: "6",
  r: "1.5"
}, "1"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M7 11h14v2H7zm0 6h14v2H7zm-3 2.5c.82 0 1.5-.68 1.5-1.5s-.67-1.5-1.5-1.5-1.5.68-1.5 1.5.68 1.5 1.5 1.5"
}, "2"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "4",
  cy: "12",
  r: "1.5"
}, "3")], 'FormatListBulletedTwoTone');