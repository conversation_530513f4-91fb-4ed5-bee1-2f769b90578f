"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M19.75 8c-.69 0-1.25.56-1.25 1.25V15H18c-1.65 0-3 1.35-3 3h-1c0-2.04 1.53-3.72 3.5-3.97V3.25c0-.69-.56-1.25-1.25-1.25S15 2.56 15 3.25V11h-1V1.25C14 .56 13.44 0 12.75 0S11.5.56 11.5 1.25V11h-1V2.75c0-.69-.56-1.25-1.25-1.25S8 2.06 8 2.75V12H7V5.75c0-.69-.56-1.25-1.25-1.25S4.5 5.06 4.5 5.75v10c0 4.56 3.69 8.25 8.25 8.25S21 20.31 21 15.75v-6.5C21 8.56 20.44 8 19.75 8"
}), 'FrontHand');