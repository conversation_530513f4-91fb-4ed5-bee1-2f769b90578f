import React from 'react';
import {
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Paper,
  Typography,
  Box
} from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronLeft } from '@fortawesome/free-solid-svg-icons';

const CategoryList = ({ categories, selectedCategory, onCategorySelect }) => {
  return (
    <Paper 
      elevation={1} 
      sx={{ 
        height: '100%', 
        minWidth: 200,
        borderRadius: 0,
        borderRight: '1px solid #e0e0e0'
      }}
    >
      <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0' }}>
        <Typography variant="h6" component="h2" sx={{ fontWeight: 'bold' }}>
          分类列表
        </Typography>
      </Box>
      
      <List sx={{ p: 0 }}>
        {categories.map((category) => (
          <ListItem key={category.id} disablePadding>
            <ListItemButton
              selected={selectedCategory === category.id}
              onClick={() => onCategorySelect(category.id)}
              sx={{
                py: 1.5,
                px: 2,
                '&.Mui-selected': {
                  backgroundColor: '#e3f2fd',
                  borderRight: '3px solid #1976d2',
                  '& .MuiListItemText-primary': {
                    fontWeight: 'bold',
                    color: '#1976d2'
                  }
                },
                '&:hover': {
                  backgroundColor: '#f5f5f5'
                }
              }}
            >
              <ListItemText 
                primary={category.name}
                sx={{
                  '& .MuiListItemText-primary': {
                    fontSize: '0.95rem'
                  }
                }}
              />
              {selectedCategory === category.id && category.id === 'favorite' && (
                <FontAwesomeIcon 
                  icon={faChevronLeft} 
                  style={{ 
                    fontSize: '12px', 
                    color: '#1976d2',
                    marginLeft: '8px'
                  }} 
                />
              )}
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Paper>
  );
};

export default CategoryList;
