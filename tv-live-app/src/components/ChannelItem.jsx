import React from 'react';
import {
  Box,
  Typography,
  Icon<PERSON>utton,
  Card,
  CardContent
} from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHeart, faHome } from '@fortawesome/free-solid-svg-icons';

const ChannelItem = ({ channel, onToggleFavorite }) => {
  return (
    <Card 
      elevation={0} 
      sx={{ 
        mb: 1, 
        border: '1px solid #e0e0e0',
        borderRadius: 1,
        '&:hover': {
          backgroundColor: '#f9f9f9'
        }
      }}
    >
      <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          {/* 台标区域 */}
          <Box
            sx={{
              width: 80,
              height: 60,
              border: '1px solid #ccc',
              borderRadius: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#f5f5f5',
              fontSize: '24px',
              flexShrink: 0
            }}
          >
            {channel.logo}
          </Box>

          {/* 频道信息 */}
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
              <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                {channel.id}
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                {channel.name}
              </Typography>
            </Box>
            
            <Typography 
              variant="body2" 
              color="text.secondary"
              sx={{ 
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              正在播放: {channel.currentProgram}
            </Typography>
          </Box>

          {/* 图标区域 */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexShrink: 0 }}>
            {channel.isHome && (
              <FontAwesomeIcon 
                icon={faHome} 
                style={{ 
                  fontSize: '16px', 
                  color: '#666'
                }} 
              />
            )}
            
            <IconButton
              size="small"
              onClick={() => onToggleFavorite(channel.id)}
              sx={{ 
                color: channel.isFavorite ? '#e91e63' : '#ccc',
                '&:hover': {
                  color: '#e91e63'
                }
              }}
            >
              <FontAwesomeIcon 
                icon={faHeart} 
                style={{ fontSize: '16px' }} 
              />
            </IconButton>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ChannelItem;
