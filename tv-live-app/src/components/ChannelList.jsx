import React from 'react';
import {
  Box,
  Typography,
  Button,
  Paper
} from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCog } from '@fortawesome/free-solid-svg-icons';
import ChannelItem from './ChannelItem.jsx';

const ChannelList = ({ 
  channels, 
  selectedCategory, 
  categoryName, 
  region,
  onToggleFavorite, 
  onOpenSettings 
}) => {
  // 根据选中的分类过滤频道
  const filteredChannels = channels.filter(channel => {
    if (selectedCategory === 'all') return true;
    if (selectedCategory === 'favorite') return channel.isFavorite;
    return channel.category.toLowerCase() === selectedCategory.toLowerCase();
  });

  const favoriteCount = channels.filter(c => c.isFavorite).length;

  return (
    <Paper 
      elevation={0} 
      sx={{ 
        height: '100%', 
        flex: 1,
        borderRadius: 0,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* 头部区域 */}
      <Box sx={{ 
        p: 2, 
        borderBottom: '1px solid #e0e0e0',
        backgroundColor: '#fafafa'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
            {categoryName} ({categoryName === 'Favorite List' ? `收藏列表` : categoryName})
          </Typography>
          <Typography variant="body2" color="text.secondary">
            频道数量: {filteredChannels.length}
          </Typography>
        </Box>
        
        {selectedCategory === 'favorite' && (
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
              {region}
            </Typography>
            <Button
              variant="outlined"
              size="small"
              startIcon={<FontAwesomeIcon icon={faCog} />}
              onClick={onOpenSettings}
              sx={{
                textTransform: 'none',
                borderRadius: 1
              }}
            >
              设置 &gt;
            </Button>
          </Box>
        )}
      </Box>

      {/* 频道列表区域 */}
      <Box sx={{ 
        flex: 1, 
        overflow: 'auto', 
        p: 2,
        backgroundColor: '#fff'
      }}>
        {filteredChannels.length > 0 ? (
          filteredChannels.map((channel) => (
            <ChannelItem
              key={channel.id}
              channel={channel}
              onToggleFavorite={onToggleFavorite}
              selectedCategory={selectedCategory}
            />
          ))
        ) : (
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '200px' 
          }}>
            <Typography variant="body1" color="text.secondary">
              该分类下暂无频道
            </Typography>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default ChannelList;
