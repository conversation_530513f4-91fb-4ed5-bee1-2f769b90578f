import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Stack
} from '@mui/material';

const SettingsDialog = ({ 
  open, 
  onClose, 
  region, 
  onOpenZipCodeDialog 
}) => {
  const handleInputZipCode = () => {
    onOpenZipCodeDialog();
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: 300
        }
      }}
    >
      <DialogTitle sx={{ 
        textAlign: 'center', 
        fontWeight: 'bold',
        borderBottom: '1px solid #e0e0e0'
      }}>
        地区设置
      </DialogTitle>
      
      <DialogContent sx={{ pt: 3, pb: 2 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            当前地区: {region}
          </Typography>
        </Box>

        <Stack spacing={2} sx={{ alignItems: 'center' }}>
          <Button
            variant="outlined"
            onClick={handleInputZipCode}
            sx={{
              minWidth: 150,
              borderRadius: 1,
              textTransform: 'none',
              py: 1
            }}
          >
            输入邮编
          </Button>

          <Button
            variant="contained"
            sx={{
              minWidth: 150,
              borderRadius: 1,
              textTransform: 'none',
              py: 1
            }}
          >
            更新
          </Button>
        </Stack>
      </DialogContent>
      
      <DialogActions sx={{ 
        justifyContent: 'center', 
        pb: 3,
        pt: 1
      }}>
        <Button
          variant="outlined"
          onClick={onClose}
          sx={{
            minWidth: 100,
            borderRadius: 1,
            textTransform: 'none'
          }}
        >
          取消
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SettingsDialog;
