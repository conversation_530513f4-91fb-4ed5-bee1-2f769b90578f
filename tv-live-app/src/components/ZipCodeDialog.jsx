import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Box
} from '@mui/material';

const ZipCodeDialog = ({ open, onClose, onConfirm }) => {
  const [zipCode, setZipCode] = useState('');

  const handleConfirm = () => {
    if (zipCode.trim()) {
      onConfirm(zipCode.trim());
      setZipCode('');
    }
  };

  const handleClose = () => {
    setZipCode('');
    onClose();
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: 200
        }
      }}
    >
      <DialogTitle sx={{ 
        textAlign: 'center', 
        fontWeight: 'bold',
        borderBottom: '1px solid #e0e0e0'
      }}>
        输入邮编
      </DialogTitle>
      
      <DialogContent sx={{ pt: 3, pb: 2 }}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            请输入您所在地区的邮政编码:
          </Typography>
          
          <TextField
            fullWidth
            variant="outlined"
            value={zipCode}
            onChange={(e) => setZipCode(e.target.value)}
            placeholder="请输入邮编"
            autoFocus
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 1
              }
            }}
          />
        </Box>
      </DialogContent>
      
      <DialogActions sx={{ 
        justifyContent: 'center', 
        pb: 3,
        pt: 1
      }}>
        <Button
          variant="contained"
          onClick={handleConfirm}
          disabled={!zipCode.trim()}
          sx={{
            minWidth: 100,
            borderRadius: 1,
            textTransform: 'none'
          }}
        >
          确定
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ZipCodeDialog;
