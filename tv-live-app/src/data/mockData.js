// 模拟频道数据
export const channels = [
  {
    id: '001',
    name: 'CCTV-1',
    logo: '🏛️',
    currentProgram: '新闻联播',
    category: 'News',
    isFavorite: true,
    isHome: false
  },
  {
    id: '002',
    name: 'CCTV-5',
    logo: '⚽',
    currentProgram: '世界杯足球赛',
    category: 'Sports',
    isFavorite: true,
    isHome: false
  },
  {
    id: '003',
    name: '湖南卫视',
    logo: '🎭',
    currentProgram: '快乐大本营',
    category: 'Entertainment',
    isFavorite: false,
    isHome: true
  },
  {
    id: '004',
    name: '江苏卫视',
    logo: '💝',
    currentProgram: '非诚勿扰',
    category: 'Entertainment',
    isFavorite: true,
    isHome: true
  },
  {
    id: '005',
    name: '浙江卫视',
    logo: '🏃',
    currentProgram: '奔跑吧兄弟',
    category: 'Entertainment',
    isFavorite: true,
    isHome: false
  }
];

// 频道分类
export const categories = [
  { id: 'all', name: 'All Channels', count: channels.length },
  { id: 'news', name: 'News', count: channels.filter(c => c.category === 'News').length },
  { id: 'sports', name: 'Sports', count: channels.filter(c => c.category === 'Sports').length },
  { id: 'movies', name: 'Movies', count: channels.filter(c => c.category === 'Movies').length },
  { id: 'favorite', name: 'Favorite List', count: channels.filter(c => c.isFavorite).length },
  { id: 'entertainment', name: 'Entertainment', count: channels.filter(c => c.category === 'Entertainment').length },
  { id: 'documentary', name: 'Documentary', count: channels.filter(c => c.category === 'Documentary').length },
  { id: 'music', name: 'Music', count: channels.filter(c => c.category === 'Music').length }
];

// 地区信息
export const regionInfo = {
  current: '美国地区',
  zipCode: ''
};
